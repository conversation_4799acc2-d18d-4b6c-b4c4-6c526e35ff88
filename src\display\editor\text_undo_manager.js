/* Copyright 2024 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 文本编辑撤销管理器
 * 专门处理文本编辑操作的撤销和重做功能
 */
class TextEditUndoManager {
  // 私有属性
  #commands = [];
  #currentIndex = -1;
  #maxCommands = 100;
  #groupTimeout = 1000; // 1秒内的连续操作合并
  #lastCommandTime = 0;
  #eventBus = null;

  /**
   * 构造函数
   * @param {Object} eventBus - 事件总线
   */
  constructor(eventBus) {
    this.#eventBus = eventBus;
    this._bindKeyboardShortcuts();
  }

  /**
   * 绑定键盘快捷键
   * @private
   */
  _bindKeyboardShortcuts() {
    document.addEventListener("keydown", (event) => {
      // Ctrl+Z 撤销
      if (event.ctrlKey && event.key === "z" && !event.shiftKey) {
        event.preventDefault();
        this.undo();
      }
      // Ctrl+Y 或 Ctrl+Shift+Z 重做
      else if (
        (event.ctrlKey && event.key === "y") ||
        (event.ctrlKey && event.shiftKey && event.key === "Z")
      ) {
        event.preventDefault();
        this.redo();
      }
    });
  }

  /**
   * 执行命令并添加到历史栈
   * @param {Object} command - 要执行的命令
   */
  executeCommand(command) {
    // 执行命令
    command.execute();

    // 检查是否需要与上一个命令合并
    if (this._shouldGroupWithPrevious(command)) {
      this._groupWithPrevious(command);
    } else {
      this._addCommand(command);
    }

    this.#lastCommandTime = Date.now();
    this._notifyStateChange();
  }

  /**
   * 添加命令到历史栈
   * @param {Object} command - 要添加的命令
   * @private
   */
  _addCommand(command) {
    // 如果当前不在栈顶，删除后续的命令
    if (this.#currentIndex < this.#commands.length - 1) {
      this.#commands.splice(this.#currentIndex + 1);
    }

    // 添加新命令
    this.#commands.push(command);
    this.#currentIndex++;

    // 限制历史记录数量
    if (this.#commands.length > this.#maxCommands) {
      this.#commands.shift();
      this.#currentIndex--;
    }
  }

  /**
   * 检查是否应该与前一个命令合并
   * @param {Object} command - 当前命令
   * @returns {boolean} 是否应该合并
   * @private
   */
  _shouldGroupWithPrevious(command) {
    if (this.#commands.length === 0) return false;

    const lastCommand = this.#commands[this.#currentIndex];
    const timeDiff = Date.now() - this.#lastCommandTime;

    // 检查时间窗口
    if (timeDiff > this.#groupTimeout) return false;

    // 检查命令类型
    if (command.type !== lastCommand.type) return false;

    // 检查是否是同一个编辑器
    if (command.editor !== lastCommand.editor) return false;

    // 检查是否是连续的文本编辑
    if (command.type === "text-edit") {
      return this._isConsecutiveTextEdit(lastCommand, command);
    }

    return false;
  }

  /**
   * 检查是否是连续的文本编辑
   * @param {Object} lastCommand - 上一个命令
   * @param {Object} currentCommand - 当前命令
   * @returns {boolean} 是否是连续编辑
   * @private
   */
  _isConsecutiveTextEdit(lastCommand, currentCommand) {
    // 简单的连续性检查：新文本是否基于上一个命令的结果
    return lastCommand.newText === currentCommand.oldText;
  }

  /**
   * 与前一个命令合并
   * @param {Object} command - 要合并的命令
   * @private
   */
  _groupWithPrevious(command) {
    const lastCommand = this.#commands[this.#currentIndex];
    
    // 更新最后一个命令的结果
    lastCommand.newText = command.newText;
    lastCommand.timestamp = command.timestamp;
  }

  /**
   * 撤销操作
   * @returns {boolean} 是否成功撤销
   */
  undo() {
    if (!this.canUndo()) return false;

    const command = this.#commands[this.#currentIndex];
    command.undo();
    this.#currentIndex--;

    this._notifyStateChange();
    this._showUndoNotification(command);

    return true;
  }

  /**
   * 重做操作
   * @returns {boolean} 是否成功重做
   */
  redo() {
    if (!this.canRedo()) return false;

    this.#currentIndex++;
    const command = this.#commands[this.#currentIndex];
    command.execute();

    this._notifyStateChange();
    this._showRedoNotification(command);

    return true;
  }

  /**
   * 检查是否可以撤销
   * @returns {boolean} 是否可以撤销
   */
  canUndo() {
    return this.#currentIndex >= 0;
  }

  /**
   * 检查是否可以重做
   * @returns {boolean} 是否可以重做
   */
  canRedo() {
    return this.#currentIndex < this.#commands.length - 1;
  }

  /**
   * 获取撤销历史
   * @returns {Array} 命令历史数组
   */
  getHistory() {
    return this.#commands.map((command, index) => ({
      description: command.getDescription?.() || command.type,
      timestamp: command.timestamp,
      isCurrent: index === this.#currentIndex,
    }));
  }

  /**
   * 清空历史记录
   */
  clear() {
    this.#commands = [];
    this.#currentIndex = -1;
    this._notifyStateChange();
  }

  /**
   * 通知状态变化
   * @private
   */
  _notifyStateChange() {
    if (this.#eventBus) {
      this.#eventBus.dispatch("undoStateChanged", {
        canUndo: this.canUndo(),
        canRedo: this.canRedo(),
        historyLength: this.#commands.length,
        currentIndex: this.#currentIndex,
      });
    }
  }

  /**
   * 显示撤销通知
   * @param {Object} command - 被撤销的命令
   * @private
   */
  _showUndoNotification(command) {
    if (this.#eventBus) {
      this.#eventBus.dispatch("showUndoNotification", {
        message: `已撤销: ${command.getDescription?.() || command.type}`,
        command,
      });
    }
  }

  /**
   * 显示重做通知
   * @param {Object} command - 被重做的命令
   * @private
   */
  _showRedoNotification(command) {
    if (this.#eventBus) {
      this.#eventBus.dispatch("showRedoNotification", {
        message: `已重做: ${command.getDescription?.() || command.type}`,
        command,
      });
    }
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      totalCommands: this.#commands.length,
      currentIndex: this.#currentIndex,
      canUndo: this.canUndo(),
      canRedo: this.canRedo(),
      memoryUsage: this._calculateMemoryUsage(),
    };
  }

  /**
   * 计算内存使用量（估算）
   * @returns {number} 内存使用量（字节）
   * @private
   */
  _calculateMemoryUsage() {
    let totalSize = 0;
    
    for (const command of this.#commands) {
      // 估算每个命令的内存使用量
      totalSize += JSON.stringify(command).length * 2; // 粗略估算
    }
    
    return totalSize;
  }

  /**
   * 优化内存使用
   * 清理过期的历史记录
   */
  optimizeMemory() {
    const maxAge = 30 * 60 * 1000; // 30分钟
    const now = Date.now();
    
    // 保留最近的命令和当前索引附近的命令
    const keepCount = Math.min(50, this.#maxCommands);
    const startIndex = Math.max(0, this.#currentIndex - keepCount + 1);
    
    // 过滤掉过期的命令
    const filteredCommands = this.#commands.filter((command, index) => {
      const isRecent = (now - command.timestamp) < maxAge;
      const isNearCurrent = index >= startIndex;
      return isRecent || isNearCurrent;
    });
    
    // 更新索引
    const removedCount = this.#commands.length - filteredCommands.length;
    this.#commands = filteredCommands;
    this.#currentIndex = Math.max(-1, this.#currentIndex - removedCount);
    
    this._notifyStateChange();
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.clear();
    this.#eventBus = null;
  }
}

export { TextEditUndoManager };
