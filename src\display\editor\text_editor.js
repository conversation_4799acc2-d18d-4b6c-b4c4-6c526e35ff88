/* Copyright 2024 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// eslint-disable-next-line max-len
/** @typedef {import("./annotation_editor_layer.js").AnnotationEditorLayer} AnnotationEditorLayer */

import {
  AnnotationEditorType,
  FeatureTest,
  shadow,
  Util,
} from "../../shared/util.js";
import { AnnotationEditor } from "./editor.js";
import { bindEvents } from "./tools.js";
import { noContextMenu, stopEvent } from "../display_utils.js";

/**
 * 文本编辑器类 - 用于直接编辑PDF中的文本内容
 * 基于AnnotationEditor基类，提供文本的直接编辑功能
 */
class TextEditor extends AnnotationEditor {
  // 私有属性
  #originalTextContent = "";
  #currentTextContent = "";
  #editableElement = null;
  #textDiv = null;
  #isEditing = false;
  #textProperties = null;
  #undoManager = null;

  // 静态属性
  static _type = "textEditor";
  static _editorType = AnnotationEditorType.TEXT_EDITOR;

  /**
   * 构造函数
   * @param {Object} params - 编辑器参数
   */
  constructor(params) {
    super({ ...params, name: "textEditor" });
    
    // 初始化文本属性
    this.#textProperties = {
      fontSize: 12,
      fontFamily: "Arial",
      color: "#000000",
      transform: null,
      position: { x: 0, y: 0 },
    };

    // 绑定事件处理器
    this._bindEvents();
  }

  /**
   * 绑定事件处理器
   * @private
   */
  _bindEvents() {
    // 双击进入编辑模式
    this.div?.addEventListener("dblclick", this.#onDoubleClick.bind(this));
    
    // 键盘事件
    document.addEventListener("keydown", this.#onKeyDown.bind(this));
  }

  /**
   * 双击事件处理器
   * @param {MouseEvent} event - 鼠标事件
   * @private
   */
  #onDoubleClick(event) {
    event.preventDefault();
    event.stopPropagation();
    
    if (!this.#isEditing) {
      this.#enterEditMode();
    }
  }

  /**
   * 键盘事件处理器
   * @param {KeyboardEvent} event - 键盘事件
   * @private
   */
  #onKeyDown(event) {
    if (!this.#isEditing) return;

    switch (event.key) {
      case "Escape":
        this.#exitEditMode(false); // 取消编辑
        break;
      case "Enter":
        if (!event.shiftKey) {
          this.#exitEditMode(true); // 保存编辑
          event.preventDefault();
        }
        break;
      case "Tab":
        this.#exitEditMode(true); // 保存并切换到下一个元素
        event.preventDefault();
        break;
    }
  }

  /**
   * 进入编辑模式
   * @private
   */
  #enterEditMode() {
    if (this.#isEditing) return;

    this.#isEditing = true;
    this.#originalTextContent = this.#currentTextContent;

    // 创建可编辑元素
    this.#createEditableElement();
    
    // 隐藏原始文本
    if (this.#textDiv) {
      this.#textDiv.style.visibility = "hidden";
    }

    // 触发编辑开始事件
    this._uiManager.dispatchEvent("textEditStart", {
      editor: this,
      originalText: this.#originalTextContent,
    });
  }

  /**
   * 退出编辑模式
   * @param {boolean} save - 是否保存更改
   * @private
   */
  #exitEditMode(save = true) {
    if (!this.#isEditing) return;

    const newText = this.#editableElement?.value || "";
    
    if (save && newText !== this.#originalTextContent) {
      // 保存更改
      this.#saveTextChange(this.#originalTextContent, newText);
    }

    // 清理编辑元素
    this.#destroyEditableElement();
    
    // 显示原始文本
    if (this.#textDiv) {
      this.#textDiv.style.visibility = "visible";
    }

    this.#isEditing = false;

    // 触发编辑结束事件
    this._uiManager.dispatchEvent("textEditEnd", {
      editor: this,
      originalText: this.#originalTextContent,
      newText: save ? newText : this.#originalTextContent,
      saved: save,
    });
  }

  /**
   * 创建可编辑元素
   * @private
   */
  #createEditableElement() {
    // 创建textarea元素用于多行文本编辑
    this.#editableElement = document.createElement("textarea");
    
    // 设置基本属性
    this.#editableElement.value = this.#currentTextContent;
    this.#editableElement.className = "textEditor-editable";
    
    // 应用样式
    this.#applyTextStyles();
    
    // 设置事件监听器
    this.#editableElement.addEventListener("blur", () => {
      this.#exitEditMode(true);
    });
    
    this.#editableElement.addEventListener("input", (event) => {
      this.#onTextInput(event);
    });

    // 添加到DOM
    this.div.appendChild(this.#editableElement);
    
    // 聚焦并选中文本
    this.#editableElement.focus();
    this.#editableElement.select();
  }

  /**
   * 销毁可编辑元素
   * @private
   */
  #destroyEditableElement() {
    if (this.#editableElement) {
      this.#editableElement.remove();
      this.#editableElement = null;
    }
  }

  /**
   * 应用文本样式
   * @private
   */
  #applyTextStyles() {
    if (!this.#editableElement || !this.#textProperties) return;

    const style = this.#editableElement.style;
    const props = this.#textProperties;

    // 基本样式
    style.fontSize = `${props.fontSize}px`;
    style.fontFamily = props.fontFamily;
    style.color = props.color;
    style.backgroundColor = "rgba(255, 255, 255, 0.9)";
    style.border = "2px solid #0066cc";
    style.borderRadius = "2px";
    style.padding = "2px";
    style.resize = "none";
    style.overflow = "hidden";
    
    // 位置和变换
    if (props.transform) {
      style.transform = props.transform;
    }
    
    // 自动调整大小
    this.#autoResize();
  }

  /**
   * 自动调整编辑框大小
   * @private
   */
  #autoResize() {
    if (!this.#editableElement) return;

    const element = this.#editableElement;
    element.style.height = "auto";
    element.style.height = `${element.scrollHeight}px`;
    
    // 调整宽度以适应内容
    const tempSpan = document.createElement("span");
    tempSpan.style.visibility = "hidden";
    tempSpan.style.position = "absolute";
    tempSpan.style.fontSize = element.style.fontSize;
    tempSpan.style.fontFamily = element.style.fontFamily;
    tempSpan.textContent = element.value || "W";
    
    document.body.appendChild(tempSpan);
    const textWidth = tempSpan.offsetWidth;
    document.body.removeChild(tempSpan);
    
    element.style.width = `${Math.max(textWidth + 20, 100)}px`;
  }

  /**
   * 文本输入事件处理器
   * @param {InputEvent} event - 输入事件
   * @private
   */
  #onTextInput(event) {
    // 自动调整大小
    this.#autoResize();
    
    // 实时更新预览（可选）
    this.#currentTextContent = event.target.value;
  }

  /**
   * 保存文本更改
   * @param {string} oldText - 原始文本
   * @param {string} newText - 新文本
   * @private
   */
  #saveTextChange(oldText, newText) {
    if (oldText === newText) return;

    // 更新当前文本内容
    this.#currentTextContent = newText;
    
    // 更新显示的文本
    if (this.#textDiv) {
      this.#textDiv.textContent = newText;
    }

    // 创建撤销命令
    const command = new TextEditCommand({
      type: "text-edit",
      editor: this,
      oldText,
      newText,
      textProperties: { ...this.#textProperties },
    });

    // 执行命令（会自动添加到撤销历史）
    this._uiManager.executeCommand(command);
  }

  /**
   * 设置文本内容
   * @param {string} text - 文本内容
   */
  setTextContent(text) {
    this.#currentTextContent = text;
    if (this.#textDiv) {
      this.#textDiv.textContent = text;
    }
  }

  /**
   * 获取文本内容
   * @returns {string} 当前文本内容
   */
  getTextContent() {
    return this.#currentTextContent;
  }

  /**
   * 设置文本属性
   * @param {Object} properties - 文本属性
   */
  setTextProperties(properties) {
    this.#textProperties = { ...this.#textProperties, ...properties };
  }

  /**
   * 获取文本属性
   * @returns {Object} 文本属性
   */
  getTextProperties() {
    return { ...this.#textProperties };
  }

  /**
   * 序列化编辑器数据
   * @returns {Object} 序列化数据
   */
  serialize() {
    return {
      ...super.serialize(),
      textContent: this.#currentTextContent,
      textProperties: this.#textProperties,
    };
  }

  /**
   * 反序列化编辑器数据
   * @param {Object} data - 序列化数据
   */
  deserialize(data) {
    super.deserialize(data);
    
    if (data.textContent) {
      this.setTextContent(data.textContent);
    }
    
    if (data.textProperties) {
      this.setTextProperties(data.textProperties);
    }
  }

  /**
   * 销毁编辑器
   */
  destroy() {
    this.#exitEditMode(false);
    super.destroy();
  }
}

/**
 * 文本编辑命令类 - 用于撤销/重做功能
 */
class TextEditCommand {
  constructor({ type, editor, oldText, newText, textProperties }) {
    this.type = type;
    this.editor = editor;
    this.oldText = oldText;
    this.newText = newText;
    this.textProperties = textProperties;
    this.timestamp = Date.now();
  }

  /**
   * 执行命令
   */
  execute() {
    this.editor.setTextContent(this.newText);
  }

  /**
   * 撤销命令
   */
  undo() {
    this.editor.setTextContent(this.oldText);
  }

  /**
   * 获取命令描述
   * @returns {string} 命令描述
   */
  getDescription() {
    return `文本编辑: "${this.oldText}" → "${this.newText}"`;
  }
}

export { TextEditor, TextEditCommand };
