/* Copyright 2024 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/** @typedef {import("./display_utils").PageViewport} PageViewport */
/** @typedef {import("./api").TextContent} TextContent */

import { TextLayer } from "./text_layer.js";
import { TextEditor } from "./editor/text_editor.js";
import { shadow, warn } from "../shared/util.js";

/**
 * 可编辑文本层类
 * 扩展TextLayer，添加文本编辑功能
 */
class EditableTextLayer extends TextLayer {
  // 私有属性
  #editingEnabled = false;
  #textEditors = new Map();
  #selectedTextDiv = null;
  #eventBus = null;
  #undoManager = null;
  #editMode = false;

  /**
   * 构造函数
   * @param {Object} options - 配置选项
   */
  constructor(options) {
    super(options);
    
    this.#eventBus = options.eventBus;
    this.#undoManager = options.undoManager;
    
    // 绑定事件处理器
    this._bindEditingEvents();
  }

  /**
   * 绑定编辑相关事件
   * @private
   */
  _bindEditingEvents() {
    // 监听文本div的点击事件
    this._container?.addEventListener("click", this.#onTextClick.bind(this));
    
    // 监听双击事件进入编辑模式
    this._container?.addEventListener("dblclick", this.#onTextDoubleClick.bind(this));
    
    // 监听键盘事件
    document.addEventListener("keydown", this.#onKeyDown.bind(this));
    
    // 监听编辑模式切换事件
    if (this.#eventBus) {
      this.#eventBus._on("editModeChanged", this.#onEditModeChanged.bind(this));
    }
  }

  /**
   * 文本点击事件处理器
   * @param {MouseEvent} event - 鼠标事件
   * @private
   */
  #onTextClick(event) {
    if (!this.#editingEnabled) return;

    const textDiv = this._findTextDiv(event.target);
    if (textDiv) {
      this.#selectTextDiv(textDiv);
    }
  }

  /**
   * 文本双击事件处理器
   * @param {MouseEvent} event - 鼠标事件
   * @private
   */
  #onTextDoubleClick(event) {
    if (!this.#editingEnabled) return;

    event.preventDefault();
    event.stopPropagation();

    const textDiv = this._findTextDiv(event.target);
    if (textDiv) {
      this.#startEditingTextDiv(textDiv);
    }
  }

  /**
   * 键盘事件处理器
   * @param {KeyboardEvent} event - 键盘事件
   * @private
   */
  #onKeyDown(event) {
    if (!this.#editingEnabled || !this.#selectedTextDiv) return;

    switch (event.key) {
      case "Enter":
        if (!event.shiftKey) {
          this.#startEditingTextDiv(this.#selectedTextDiv);
          event.preventDefault();
        }
        break;
      case "Delete":
      case "Backspace":
        this.#deleteSelectedText();
        event.preventDefault();
        break;
      case "Escape":
        this.#clearSelection();
        break;
    }
  }

  /**
   * 编辑模式变化事件处理器
   * @param {Object} event - 事件对象
   * @private
   */
  #onEditModeChanged(event) {
    this.#editMode = event.enabled;
    this.setEditingEnabled(event.enabled);
  }

  /**
   * 查找文本div元素
   * @param {Element} element - 目标元素
   * @returns {Element|null} 文本div元素
   * @private
   */
  _findTextDiv(element) {
    // 向上查找，直到找到文本div或到达容器
    while (element && element !== this._container) {
      if (element.classList?.contains("textLayer") && element.textContent) {
        return element;
      }
      element = element.parentElement;
    }
    return null;
  }

  /**
   * 选择文本div
   * @param {Element} textDiv - 要选择的文本div
   * @private
   */
  #selectTextDiv(textDiv) {
    // 清除之前的选择
    this.#clearSelection();
    
    // 设置新的选择
    this.#selectedTextDiv = textDiv;
    textDiv.classList.add("selected");
    
    // 触发选择事件
    if (this.#eventBus) {
      this.#eventBus.dispatch("textSelected", {
        textDiv,
        text: textDiv.textContent,
        bounds: textDiv.getBoundingClientRect(),
      });
    }
  }

  /**
   * 清除文本选择
   * @private
   */
  #clearSelection() {
    if (this.#selectedTextDiv) {
      this.#selectedTextDiv.classList.remove("selected");
      this.#selectedTextDiv = null;
      
      if (this.#eventBus) {
        this.#eventBus.dispatch("textSelectionCleared");
      }
    }
  }

  /**
   * 开始编辑文本div
   * @param {Element} textDiv - 要编辑的文本div
   * @private
   */
  #startEditingTextDiv(textDiv) {
    if (!textDiv) return;

    // 获取文本属性
    const textProperties = this._extractTextProperties(textDiv);
    const originalText = textDiv.textContent || "";

    // 创建文本编辑器
    const editor = this._createTextEditor(textDiv, textProperties);
    
    // 存储编辑器
    this.#textEditors.set(textDiv, editor);
    
    // 开始编辑
    editor.startEditing(originalText);
  }

  /**
   * 提取文本属性
   * @param {Element} textDiv - 文本div元素
   * @returns {Object} 文本属性
   * @private
   */
  _extractTextProperties(textDiv) {
    const computedStyle = window.getComputedStyle(textDiv);
    const rect = textDiv.getBoundingClientRect();
    
    return {
      fontSize: parseFloat(computedStyle.fontSize) || 12,
      fontFamily: computedStyle.fontFamily || "Arial",
      color: computedStyle.color || "#000000",
      fontWeight: computedStyle.fontWeight || "normal",
      fontStyle: computedStyle.fontStyle || "normal",
      textAlign: computedStyle.textAlign || "left",
      lineHeight: computedStyle.lineHeight || "normal",
      transform: computedStyle.transform || "none",
      position: {
        x: rect.left,
        y: rect.top,
        width: rect.width,
        height: rect.height,
      },
      // 保存原始的变换矩阵信息
      matrix: this._getTransformMatrix(textDiv),
    };
  }

  /**
   * 获取变换矩阵
   * @param {Element} element - 元素
   * @returns {Array|null} 变换矩阵
   * @private
   */
  _getTransformMatrix(element) {
    const transform = window.getComputedStyle(element).transform;
    if (transform === "none") return null;
    
    const matrixMatch = transform.match(/matrix\(([^)]+)\)/);
    if (matrixMatch) {
      return matrixMatch[1].split(",").map(n => parseFloat(n.trim()));
    }
    
    return null;
  }

  /**
   * 创建文本编辑器
   * @param {Element} textDiv - 文本div元素
   * @param {Object} textProperties - 文本属性
   * @returns {TextEditor} 文本编辑器实例
   * @private
   */
  _createTextEditor(textDiv, textProperties) {
    const editor = new TextEditor({
      textDiv,
      textProperties,
      undoManager: this.#undoManager,
      eventBus: this.#eventBus,
    });

    // 监听编辑完成事件
    editor.addEventListener("editComplete", (event) => {
      this._onTextEditComplete(textDiv, event.detail);
    });

    // 监听编辑取消事件
    editor.addEventListener("editCancelled", () => {
      this._onTextEditCancelled(textDiv);
    });

    return editor;
  }

  /**
   * 文本编辑完成事件处理器
   * @param {Element} textDiv - 文本div元素
   * @param {Object} editResult - 编辑结果
   * @private
   */
  _onTextEditComplete(textDiv, editResult) {
    const { originalText, newText, textProperties } = editResult;
    
    // 更新文本内容
    if (originalText !== newText) {
      textDiv.textContent = newText;
      
      // 触发文本更改事件
      if (this.#eventBus) {
        this.#eventBus.dispatch("textChanged", {
          textDiv,
          originalText,
          newText,
          textProperties,
        });
      }
    }
    
    // 清理编辑器
    this._cleanupTextEditor(textDiv);
  }

  /**
   * 文本编辑取消事件处理器
   * @param {Element} textDiv - 文本div元素
   * @private
   */
  _onTextEditCancelled(textDiv) {
    // 清理编辑器
    this._cleanupTextEditor(textDiv);
  }

  /**
   * 清理文本编辑器
   * @param {Element} textDiv - 文本div元素
   * @private
   */
  _cleanupTextEditor(textDiv) {
    const editor = this.#textEditors.get(textDiv);
    if (editor) {
      editor.destroy();
      this.#textEditors.delete(textDiv);
    }
  }

  /**
   * 删除选中的文本
   * @private
   */
  #deleteSelectedText() {
    if (!this.#selectedTextDiv) return;

    const textDiv = this.#selectedTextDiv;
    const originalText = textDiv.textContent || "";
    
    // 清空文本内容
    textDiv.textContent = "";
    
    // 触发文本删除事件
    if (this.#eventBus) {
      this.#eventBus.dispatch("textDeleted", {
        textDiv,
        originalText,
      });
    }
    
    // 清除选择
    this.#clearSelection();
  }

  /**
   * 启用或禁用文本编辑功能
   * @param {boolean} enabled - 是否启用
   */
  setEditingEnabled(enabled) {
    this.#editingEnabled = enabled;
    
    // 更新容器样式
    if (this._container) {
      this._container.classList.toggle("editable", enabled);
    }
    
    // 如果禁用编辑，清理所有编辑器
    if (!enabled) {
      this._cleanupAllEditors();
      this.#clearSelection();
    }
  }

  /**
   * 检查是否启用了编辑功能
   * @returns {boolean} 是否启用编辑
   */
  isEditingEnabled() {
    return this.#editingEnabled;
  }

  /**
   * 清理所有文本编辑器
   * @private
   */
  _cleanupAllEditors() {
    for (const [textDiv, editor] of this.#textEditors) {
      editor.destroy();
    }
    this.#textEditors.clear();
  }

  /**
   * 获取所有可编辑的文本元素
   * @returns {Array<Element>} 文本元素数组
   */
  getEditableTextElements() {
    if (!this._container) return [];
    
    return Array.from(this._container.querySelectorAll(".textLayer"))
      .filter(div => div.textContent && div.textContent.trim());
  }

  /**
   * 销毁可编辑文本层
   */
  destroy() {
    this._cleanupAllEditors();
    this.#clearSelection();
    super.destroy?.();
  }
}

export { EditableTextLayer };
